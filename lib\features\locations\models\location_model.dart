import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_model.freezed.dart';
part 'location_model.g.dart';

/// نموذج الموقع - يحل محل FolderModel
/// يدعم النظام الجديد للمواقع الـ 70 (U101-U125, C101-C145)
@freezed
class LocationModel with _$LocationModel {
  const factory LocationModel({
    required String id,
    required String locationCode,        // U101, C102, etc.
    required String locationType,        // 'U' or 'C'
    required String locationNumber,      // '101', '102', etc.
    required String locationNameAr,      // الاسم العربي
    String? locationNameEn,              // الاسم الإنجليزي
    required int sortOrder,              // ترتيب العرض
    String? category,                    // مكاتب، قاعات، مختبرات
    String? department,                  // القسم
    @Default(true) bool isActive,        // نشط/غير نشط
    @Default(true) bool isAvailable,     // متاح/غير متاح
    @Default(0) int totalPhotos,         // عدد الصور
    @Default(0) int totalVideos,         // عدد الفيديوهات
    @Default(0) int totalFiles,          // إجمالي الملفات
    DateTime? lastUsedAt,                // آخر استخدام
    @Default('') String description,     // وصف الموقع
    Map<String, dynamic>? metadata,      // بيانات إضافية
    DateTime? createdAt,                 // تاريخ الإنشاء
    DateTime? updatedAt,                 // تاريخ التحديث
  }) = _LocationModel;

  factory LocationModel.fromJson(Map<String, dynamic> json) =>
      _$LocationModelFromJson(json);

  /// إنشاء موقع من استجابة قاعدة البيانات
  factory LocationModel.fromDatabase(Map<String, dynamic> data) {
    return LocationModel(
      id: data['id'] ?? '',
      locationCode: data['location_code'] ?? '',
      locationType: data['location_type'] ?? '',
      locationNumber: data['location_number'] ?? '',
      locationNameAr: data['location_name_ar'] ?? '',
      locationNameEn: data['location_name_en'],
      sortOrder: data['sort_order'] ?? 0,
      category: data['category'],
      department: data['department'],
      isActive: data['is_active'] ?? true,
      isAvailable: data['is_available'] ?? true,
      totalPhotos: data['total_photos'] ?? 0,
      totalVideos: data['total_videos'] ?? 0,
      totalFiles: (data['total_photos'] ?? 0) + (data['total_videos'] ?? 0),
      lastUsedAt: data['last_used_at'] != null 
          ? DateTime.parse(data['last_used_at']) 
          : null,
      description: data['description'] ?? '',
      metadata: data['metadata'],
      createdAt: data['created_at'] != null 
          ? DateTime.parse(data['created_at']) 
          : null,
      updatedAt: data['updated_at'] != null 
          ? DateTime.parse(data['updated_at']) 
          : null,
    );
  }

  /// تحويل إلى خريطة لقاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'location_code': locationCode,
      'location_type': locationType,
      'location_number': locationNumber,
      'location_name_ar': locationNameAr,
      'location_name_en': locationNameEn,
      'sort_order': sortOrder,
      'category': category,
      'department': department,
      'is_active': isActive,
      'is_available': isAvailable,
      'total_photos': totalPhotos,
      'total_videos': totalVideos,
      'last_used_at': lastUsedAt?.toIso8601String(),
      'description': description,
      'metadata': metadata,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

/// حالة الموقع
enum LocationStatus {
  active,
  inactive,
  maintenance,
  unavailable,
}

/// نوع الموقع
enum LocationTypeEnum {
  u('U', 'مكاتب'),
  c('C', 'قاعات');

  const LocationTypeEnum(this.code, this.nameAr);
  
  final String code;
  final String nameAr;
  
  static LocationTypeEnum fromCode(String code) {
    return LocationTypeEnum.values.firstWhere(
      (type) => type.code == code,
      orElse: () => LocationTypeEnum.u,
    );
  }
}

/// إحصائيات الموقع
@freezed
class LocationStats with _$LocationStats {
  const factory LocationStats({
    required String locationCode,
    required String locationType,
    required String locationNameAr,
    String? locationNameEn,
    @Default(0) int totalPhotos,
    @Default(0) int totalVideos,
    @Default(0) int totalFiles,
    DateTime? lastUsedAt,
    @Default(true) bool isActive,
    @Default(0.0) double usagePercentage,
    @Default(0) int dailyAverage,
    @Default(0) int weeklyAverage,
    @Default(0) int monthlyAverage,
  }) = _LocationStats;

  factory LocationStats.fromJson(Map<String, dynamic> json) =>
      _$LocationStatsFromJson(json);

  /// إنشاء إحصائيات من استجابة دالة get_locations_with_stats
  factory LocationStats.fromDatabase(Map<String, dynamic> data) {
    return LocationStats(
      locationCode: data['location_code'] ?? '',
      locationType: data['location_type'] ?? '',
      locationNameAr: data['location_name_ar'] ?? '',
      locationNameEn: data['location_name_en'],
      totalPhotos: data['total_photos'] ?? 0,
      totalVideos: data['total_videos'] ?? 0,
      totalFiles: data['total_files'] ?? 0,
      lastUsedAt: data['last_used_at'] != null 
          ? DateTime.parse(data['last_used_at']) 
          : null,
      isActive: data['is_active'] ?? true,
      usagePercentage: (data['usage_percentage'] ?? 0.0).toDouble(),
      dailyAverage: data['daily_average'] ?? 0,
      weeklyAverage: data['weekly_average'] ?? 0,
      monthlyAverage: data['monthly_average'] ?? 0,
    );
  }
}

/// فلتر البحث في المواقع
@freezed
class LocationFilter with _$LocationFilter {
  const factory LocationFilter({
    String? searchTerm,
    String? locationType,        // 'U', 'C', or null for all
    bool? isActive,
    bool? isAvailable,
    String? category,
    String? department,
    int? minPhotos,
    int? maxPhotos,
    int? minVideos,
    int? maxVideos,
    DateTime? lastUsedAfter,
    DateTime? lastUsedBefore,
    @Default('location_code') String sortBy,
    @Default(true) bool ascending,
    @Default(50) int limit,
    @Default(0) int offset,
  }) = _LocationFilter;

  factory LocationFilter.fromJson(Map<String, dynamic> json) =>
      _$LocationFilterFromJson(json);
}
