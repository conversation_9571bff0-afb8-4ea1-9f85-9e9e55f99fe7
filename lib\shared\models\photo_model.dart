import 'package:freezed_annotation/freezed_annotation.dart';

part 'photo_model.freezed.dart';
part 'photo_model.g.dart';

/// نموذج الصورة - منفصل عن MediaModel
@freezed
class PhotoModel with _$PhotoModel {
  const factory PhotoModel({
    required String id,
    required String userId,
    required String fileName,
    String? imageUrl,
    String? storagePath,
    String? thumbnailUrl,
    String? processedImageUrl,
    
    // معلومات الملف
    int? fileSizeBytes,
    String? mimeType,
    String? fileExtension,
    
    // معلومات الموقع
    String? location,                    // للتوافق القديم
    String? locationType,                // 'U' or 'C'
    String? locationNumber,              // '101', '102'
    String? fullLocationCode,            // 'U101', 'C102'
    
    // معلومات المستخدم
    required String username,
    
    // التوقيتات
    required DateTime captureTimestamp,
    DateTime? uploadTimestamp,
    DateTime? processedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // معلومات إضافية
    @Default([]) List<String> tags,
    String? description,
    Map<String, dynamic>? cameraSettings,
    Map<String, dynamic>? gpsCoordinates,
    Map<String, dynamic>? weatherInfo,
    
    // حالة الصورة
    @Default(PhotoStatus.active) PhotoStatus status,
    @Default(false) bool isProcessed,
    Map<String, dynamic>? processingInfo,
    
    // ترتيب وتصنيف
    int? sortOrder,
    @Default(0.0) double rating,
    @Default(false) bool isFavorite,
    @Default(false) bool isPublic,
  }) = _PhotoModel;

  factory PhotoModel.fromJson(Map<String, dynamic> json) =>
      _$PhotoModelFromJson(json);

  /// إنشاء صورة من استجابة قاعدة البيانات
  factory PhotoModel.fromDatabase(Map<String, dynamic> data) {
    return PhotoModel(
      id: data['id'] ?? '',
      userId: data['user_id'] ?? '',
      fileName: data['file_name'] ?? '',
      imageUrl: data['image_url'] ?? data['url'],
      storagePath: data['storage_path'],
      thumbnailUrl: data['thumbnail_url'],
      processedImageUrl: data['processed_image_url'],
      fileSizeBytes: data['file_size_bytes'],
      mimeType: data['mime_type'],
      fileExtension: data['file_extension'],
      location: data['location'],
      locationType: data['location_type'],
      locationNumber: data['location_number'],
      fullLocationCode: data['full_location_code'],
      username: data['username'] ?? '',
      captureTimestamp: data['capture_timestamp'] != null 
          ? DateTime.parse(data['capture_timestamp']) 
          : DateTime.now(),
      uploadTimestamp: data['upload_timestamp'] != null 
          ? DateTime.parse(data['upload_timestamp']) 
          : null,
      processedAt: data['processed_at'] != null 
          ? DateTime.parse(data['processed_at']) 
          : null,
      createdAt: data['created_at'] != null 
          ? DateTime.parse(data['created_at']) 
          : null,
      updatedAt: data['updated_at'] != null 
          ? DateTime.parse(data['updated_at']) 
          : null,
      tags: data['tags'] != null 
          ? List<String>.from(data['tags']) 
          : [],
      description: data['description'],
      cameraSettings: data['camera_settings'],
      gpsCoordinates: data['gps_coordinates'],
      weatherInfo: data['weather_info'],
      status: PhotoStatus.fromString(data['status'] ?? 'active'),
      isProcessed: data['is_processed'] ?? false,
      processingInfo: data['processing_info'],
      sortOrder: data['sort_order'],
      rating: (data['rating'] ?? 0.0).toDouble(),
      isFavorite: data['is_favorite'] ?? false,
      isPublic: data['is_public'] ?? false,
    );
  }

  /// تحويل إلى خريطة لقاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'user_id': userId,
      'file_name': fileName,
      'image_url': imageUrl,
      'url': imageUrl, // للتوافق القديم
      'storage_path': storagePath,
      'thumbnail_url': thumbnailUrl,
      'processed_image_url': processedImageUrl,
      'file_size_bytes': fileSizeBytes,
      'mime_type': mimeType,
      'file_extension': fileExtension,
      'location': location,
      'location_type': locationType,
      'location_number': locationNumber,
      'full_location_code': fullLocationCode,
      'username': username,
      'capture_timestamp': captureTimestamp.toIso8601String(),
      'upload_timestamp': uploadTimestamp?.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'tags': tags,
      'description': description,
      'camera_settings': cameraSettings,
      'gps_coordinates': gpsCoordinates,
      'weather_info': weatherInfo,
      'status': status.value,
      'is_processed': isProcessed,
      'processing_info': processingInfo,
      'sort_order': sortOrder,
      'rating': rating,
      'is_favorite': isFavorite,
      'is_public': isPublic,
    };
  }
}

/// حالة الصورة
enum PhotoStatus {
  active('active', 'نشطة'),
  deleted('deleted', 'محذوفة'),
  archived('archived', 'مؤرشفة'),
  processing('processing', 'قيد المعالجة'),
  failed('failed', 'فشلت');

  const PhotoStatus(this.value, this.nameAr);
  
  final String value;
  final String nameAr;
  
  static PhotoStatus fromString(String value) {
    return PhotoStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => PhotoStatus.active,
    );
  }
}

/// فلتر البحث في الصور
@freezed
class PhotoFilter with _$PhotoFilter {
  const factory PhotoFilter({
    String? searchTerm,
    String? userId,
    String? locationType,
    String? locationCode,
    String? username,
    PhotoStatus? status,
    DateTime? captureAfter,
    DateTime? captureBefore,
    DateTime? uploadAfter,
    DateTime? uploadBefore,
    List<String>? tags,
    bool? isFavorite,
    bool? isPublic,
    double? minRating,
    int? minFileSize,
    int? maxFileSize,
    @Default('capture_timestamp') String sortBy,
    @Default(false) bool ascending,
    @Default(50) int limit,
    @Default(0) int offset,
  }) = _PhotoFilter;

  factory PhotoFilter.fromJson(Map<String, dynamic> json) =>
      _$PhotoFilterFromJson(json);
}
