import 'package:freezed_annotation/freezed_annotation.dart';

part 'user_model.freezed.dart';
part 'user_model.g.dart';

/// نموذج المستخدم المحدث - متوافق مع قاعدة البيانات الجديدة
@freezed
class UserModel with _$UserModel {
  const factory UserModel({
    required String id,
    required String nationalId,
    required String fullName,
    String? email,
    String? phone,
    String? avatarUrl,
    
    // إعدادات الحساب
    @Default(true) bool isActive,
    @Default(false) bool isAdmin,
    @Default('user') String accountType,
    
    // حدود الحساب
    @Default(3) int maxDevices,
    @Default(1000) int storageQuotaMb,
    
    // معلومات إضافية
    String? department,
    String? position,
    String? notes,
    
    // إحصائيات
    @Default(0) int totalPhotos,
    @Default(0) int totalVideos,
    @Default(0.0) double totalStorageMb,
    @Default(0) int activeDevices,
    
    // التوقيتات
    DateTime? lastLogin,
    DateTime? passwordChangedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) = _UserModel;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  /// إنشاء مستخدم من استجابة قاعدة البيانات
  factory UserModel.fromDatabase(Map<String, dynamic> data) {
    return UserModel(
      id: data['id'] ?? '',
      nationalId: data['national_id'] ?? '',
      fullName: data['full_name'] ?? '',
      email: data['email'],
      phone: data['phone'],
      avatarUrl: data['avatar_url'],
      isActive: data['is_active'] ?? true,
      isAdmin: data['is_admin'] ?? false,
      accountType: data['account_type'] ?? 'user',
      maxDevices: data['max_devices'] ?? 3,
      storageQuotaMb: data['storage_quota_mb'] ?? 1000,
      department: data['department'],
      position: data['position'],
      notes: data['notes'],
      totalPhotos: data['total_photos'] ?? 0,
      totalVideos: data['total_videos'] ?? 0,
      totalStorageMb: (data['total_storage_mb'] ?? 0.0).toDouble(),
      activeDevices: data['active_devices'] ?? 0,
      lastLogin: data['last_login'] != null 
          ? DateTime.parse(data['last_login']) 
          : null,
      passwordChangedAt: data['password_changed_at'] != null 
          ? DateTime.parse(data['password_changed_at']) 
          : null,
      createdAt: data['created_at'] != null 
          ? DateTime.parse(data['created_at']) 
          : null,
      updatedAt: data['updated_at'] != null 
          ? DateTime.parse(data['updated_at']) 
          : null,
      createdBy: data['created_by'],
    );
  }

  /// تحويل إلى خريطة لقاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'national_id': nationalId,
      'full_name': fullName,
      'email': email,
      'phone': phone,
      'avatar_url': avatarUrl,
      'is_active': isActive,
      'is_admin': isAdmin,
      'account_type': accountType,
      'max_devices': maxDevices,
      'storage_quota_mb': storageQuotaMb,
      'department': department,
      'position': position,
      'notes': notes,
      'last_login': lastLogin?.toIso8601String(),
      'password_changed_at': passwordChangedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'created_by': createdBy,
    };
  }
}

/// نوع الحساب
enum AccountType {
  admin('admin', 'مشرف'),
  user('user', 'مستخدم'),
  supervisor('supervisor', 'مشرف قسم');

  const AccountType(this.code, this.nameAr);
  
  final String code;
  final String nameAr;
  
  static AccountType fromCode(String code) {
    return AccountType.values.firstWhere(
      (type) => type.code == code,
      orElse: () => AccountType.user,
    );
  }
}

/// إحصائيات المستخدم
@freezed
class UserStats with _$UserStats {
  const factory UserStats({
    required String userId,
    required String fullName,
    @Default(0) int totalPhotos,
    @Default(0) int totalVideos,
    @Default(0) int totalFiles,
    @Default(0.0) double totalStorageMb,
    @Default(0) int activeDevices,
    @Default(0) int photosThisMonth,
    @Default(0) int videosThisMonth,
    DateTime? lastActivity,
    DateTime? lastLogin,
    @Default([]) List<String> mostUsedLocations,
  }) = _UserStats;

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);
}
