import 'package:freezed_annotation/freezed_annotation.dart';

part 'video_model.freezed.dart';
part 'video_model.g.dart';

/// نموذج الفيديو - منفصل عن MediaModel
@freezed
class VideoModel with _$VideoModel {
  const factory VideoModel({
    required String id,
    required String userId,
    required String fileName,
    String? videoUrl,
    String? storagePath,
    String? thumbnailUrl,
    String? processedVideoUrl,
    
    // معلومات الملف
    int? fileSizeBytes,
    String? mimeType,
    String? fileExtension,
    
    // معلومات الفيديو المحددة
    int? durationSeconds,                // مدة الفيديو
    String? resolution,                  // 1920x1080, 4K
    int? fps,                           // إطارات في الثانية
    String? codec,                      // H.264, H.265
    int? bitrate,                       // معدل البت
    String? audioCodec,                 // AAC, MP3
    int? audioBitrate,                  // معدل بت الصوت
    
    // معلومات الموقع
    String? location,                    // للتوافق القديم
    String? locationType,                // 'U' or 'C'
    String? locationNumber,              // '101', '102'
    String? fullLocationCode,            // 'U101', 'C102'
    
    // معلومات المستخدم
    required String username,
    
    // التوقيتات
    required DateTime captureTimestamp,
    DateTime? uploadTimestamp,
    DateTime? processedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
    
    // معلومات إضافية
    @Default([]) List<String> tags,
    String? description,
    Map<String, dynamic>? cameraSettings,
    Map<String, dynamic>? gpsCoordinates,
    Map<String, dynamic>? weatherInfo,
    
    // حالة الفيديو
    @Default(VideoStatus.active) VideoStatus status,
    @Default(false) bool isProcessed,
    Map<String, dynamic>? processingInfo,
    
    // ترتيب وتصنيف
    int? sortOrder,
    @Default(0.0) double rating,
    @Default(false) bool isFavorite,
    @Default(false) bool isPublic,
    
    // معلومات التشغيل
    @Default(0) int viewCount,
    @Default(0) int downloadCount,
    DateTime? lastViewedAt,
  }) = _VideoModel;

  factory VideoModel.fromJson(Map<String, dynamic> json) =>
      _$VideoModelFromJson(json);

  /// إنشاء فيديو من استجابة قاعدة البيانات
  factory VideoModel.fromDatabase(Map<String, dynamic> data) {
    return VideoModel(
      id: data['id'] ?? '',
      userId: data['user_id'] ?? '',
      fileName: data['file_name'] ?? '',
      videoUrl: data['video_url'] ?? data['url'],
      storagePath: data['storage_path'],
      thumbnailUrl: data['thumbnail_url'],
      processedVideoUrl: data['processed_video_url'],
      fileSizeBytes: data['file_size_bytes'],
      mimeType: data['mime_type'],
      fileExtension: data['file_extension'],
      durationSeconds: data['duration_seconds'],
      resolution: data['resolution'],
      fps: data['fps'],
      codec: data['codec'],
      bitrate: data['bitrate'],
      audioCodec: data['audio_codec'],
      audioBitrate: data['audio_bitrate'],
      location: data['location'],
      locationType: data['location_type'],
      locationNumber: data['location_number'],
      fullLocationCode: data['full_location_code'],
      username: data['username'] ?? '',
      captureTimestamp: data['capture_timestamp'] != null 
          ? DateTime.parse(data['capture_timestamp']) 
          : DateTime.now(),
      uploadTimestamp: data['upload_timestamp'] != null 
          ? DateTime.parse(data['upload_timestamp']) 
          : null,
      processedAt: data['processed_at'] != null 
          ? DateTime.parse(data['processed_at']) 
          : null,
      createdAt: data['created_at'] != null 
          ? DateTime.parse(data['created_at']) 
          : null,
      updatedAt: data['updated_at'] != null 
          ? DateTime.parse(data['updated_at']) 
          : null,
      tags: data['tags'] != null 
          ? List<String>.from(data['tags']) 
          : [],
      description: data['description'],
      cameraSettings: data['camera_settings'],
      gpsCoordinates: data['gps_coordinates'],
      weatherInfo: data['weather_info'],
      status: VideoStatus.fromString(data['status'] ?? 'active'),
      isProcessed: data['is_processed'] ?? false,
      processingInfo: data['processing_info'],
      sortOrder: data['sort_order'],
      rating: (data['rating'] ?? 0.0).toDouble(),
      isFavorite: data['is_favorite'] ?? false,
      isPublic: data['is_public'] ?? false,
      viewCount: data['view_count'] ?? 0,
      downloadCount: data['download_count'] ?? 0,
      lastViewedAt: data['last_viewed_at'] != null 
          ? DateTime.parse(data['last_viewed_at']) 
          : null,
    );
  }

  /// تحويل إلى خريطة لقاعدة البيانات
  Map<String, dynamic> toDatabase() {
    return {
      'id': id,
      'user_id': userId,
      'file_name': fileName,
      'video_url': videoUrl,
      'url': videoUrl, // للتوافق القديم
      'storage_path': storagePath,
      'thumbnail_url': thumbnailUrl,
      'processed_video_url': processedVideoUrl,
      'file_size_bytes': fileSizeBytes,
      'mime_type': mimeType,
      'file_extension': fileExtension,
      'duration_seconds': durationSeconds,
      'resolution': resolution,
      'fps': fps,
      'codec': codec,
      'bitrate': bitrate,
      'audio_codec': audioCodec,
      'audio_bitrate': audioBitrate,
      'location': location,
      'location_type': locationType,
      'location_number': locationNumber,
      'full_location_code': fullLocationCode,
      'username': username,
      'capture_timestamp': captureTimestamp.toIso8601String(),
      'upload_timestamp': uploadTimestamp?.toIso8601String(),
      'processed_at': processedAt?.toIso8601String(),
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'tags': tags,
      'description': description,
      'camera_settings': cameraSettings,
      'gps_coordinates': gpsCoordinates,
      'weather_info': weatherInfo,
      'status': status.value,
      'is_processed': isProcessed,
      'processing_info': processingInfo,
      'sort_order': sortOrder,
      'rating': rating,
      'is_favorite': isFavorite,
      'is_public': isPublic,
      'view_count': viewCount,
      'download_count': downloadCount,
      'last_viewed_at': lastViewedAt?.toIso8601String(),
    };
  }
}

/// حالة الفيديو
enum VideoStatus {
  active('active', 'نشط'),
  deleted('deleted', 'محذوف'),
  archived('archived', 'مؤرشف'),
  processing('processing', 'قيد المعالجة'),
  failed('failed', 'فشل');

  const VideoStatus(this.value, this.nameAr);
  
  final String value;
  final String nameAr;
  
  static VideoStatus fromString(String value) {
    return VideoStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => VideoStatus.active,
    );
  }
}
