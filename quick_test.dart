import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  print('🚀 اختبار سريع للمواقع...');
  
  try {
    // تهيئة Supabase
    await Supabase.initialize(
      url: 'https://moonmemorycamera.supabase.co',
      anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1vb25tZW1vcnljYW1lcmEiLCJyb2xlIjoiYW5vbiIsImlhdCI6MTczNjc5NzI5NywiZXhwIjoyMDUyMzczMjk3fQ.Ej6Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8',
    );

    final supabase = Supabase.instance.client;
    
    // اختبار 1: عد المواقع
    print('\n📊 اختبار عد المواقع...');
    final countResponse = await supabase
        .from('locations')
        .select('location_type')
        .count();
    
    print('✅ إجمالي المواقع: ${countResponse.count}');
    
    // اختبار 2: عد مواقع U
    final uCount = await supabase
        .from('locations')
        .select('*', const FetchOptions(count: CountOption.exact))
        .eq('location_type', 'U');
    
    print('✅ مواقع U: ${uCount.count}');
    
    // اختبار 3: عد مواقع C
    final cCount = await supabase
        .from('locations')
        .select('*', const FetchOptions(count: CountOption.exact))
        .eq('location_type', 'C');
    
    print('✅ مواقع C: ${cCount.count}');
    
    // اختبار 4: جلب أول 5 مواقع
    final locations = await supabase
        .from('locations')
        .select('location_code, location_type, location_name_ar')
        .limit(5);
    
    print('\n📍 أول 5 مواقع:');
    for (var location in locations) {
      print('   ${location['location_code']} - ${location['location_type']} - ${location['location_name_ar']}');
    }
    
    // اختبار 5: دالة الإحصائيات
    print('\n📊 اختبار دالة الإحصائيات...');
    final statsResponse = await supabase.rpc('get_locations_with_stats');
    print('✅ دالة الإحصائيات تعمل - عدد النتائج: ${statsResponse.length}');
    
    print('\n🎉 جميع الاختبارات نجحت!');
    
  } catch (e) {
    print('❌ خطأ: $e');
  }
}
