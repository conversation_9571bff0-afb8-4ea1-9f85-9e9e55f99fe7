// اختبار بعد تطبيق التحسينات
import 'dart:io';
import 'dart:convert';

void main() async {
  const supabaseUrl = 'https://xufiuvdtfusbaerwrkzb.supabase.co';
  const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1Zml1dmR0ZnVzYmFlcndya3piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNDA4MjUsImV4cCI6MjA1MDkxNjgyNX0.utjAcWoqFp0DD4IYt9Z-mVOmaLSxoh4yj_frLvOzfrE';

  print('🎉 اختبار بعد تطبيق التحسينات');
  print('=' * 50);

  try {
    final client = HttpClient();
    
    // اختبار 1: فحص جدول المواقع
    print('\n📍 اختبار 1: فحص جدول المواقع...');
    try {
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/locations?select=location_code,location_type,total_photos,total_videos&limit=5'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        print('✅ جدول المواقع يعمل: ${response.statusCode}');
        print('   عدد المواقع المعروضة: ${data.length}');
        if (data.length > 0) {
          print('   مثال: ${data[0]['location_code']} - النوع: ${data[0]['location_type']}');
        }
      } else {
        print('❌ جدول المواقع: ${response.statusCode}');
        print('   الخطأ: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في جدول المواقع: $e');
    }

    // اختبار 2: فحص دالة الإحصائيات
    print('\n📊 اختبار 2: فحص دالة الإحصائيات...');
    try {
      final request = await client.postUrl(Uri.parse('$supabaseUrl/rest/v1/rpc/get_locations_with_stats'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      request.write('{}');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        print('✅ دالة الإحصائيات تعمل: ${response.statusCode}');
        print('   عدد المواقع: ${data.length}');
        if (data.length > 0) {
          print('   مثال: ${data[0]['location_code']} - الصور: ${data[0]['total_photos']} - الفيديوهات: ${data[0]['total_videos']}');
        }
      } else {
        print('❌ دالة الإحصائيات: ${response.statusCode}');
        print('   الخطأ: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في دالة الإحصائيات: $e');
    }

    // اختبار 3: فحص دالة تحديث الإحصائيات
    print('\n🔄 اختبار 3: فحص دالة تحديث الإحصائيات...');
    try {
      final request = await client.postUrl(Uri.parse('$supabaseUrl/rest/v1/rpc/update_all_locations_statistics'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      request.write('{}');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        print('✅ دالة تحديث الإحصائيات تعمل: ${response.statusCode}');
        print('   النتيجة: $responseBody');
      } else {
        print('❌ دالة تحديث الإحصائيات: ${response.statusCode}');
        print('   الخطأ: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في دالة تحديث الإحصائيات: $e');
    }

    // اختبار 4: عد المواقع حسب النوع
    print('\n🔢 اختبار 4: عد المواقع حسب النوع...');
    try {
      // مواقع U
      final requestU = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/locations?select=count&location_type=eq.U'));
      requestU.headers.set('apikey', supabaseKey);
      requestU.headers.set('Authorization', 'Bearer $supabaseKey');
      
      final responseU = await requestU.close();
      final responseBodyU = await responseU.transform(utf8.decoder).join();
      
      // مواقع C
      final requestC = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/locations?select=count&location_type=eq.C'));
      requestC.headers.set('apikey', supabaseKey);
      requestC.headers.set('Authorization', 'Bearer $supabaseKey');
      
      final responseC = await requestC.close();
      final responseBodyC = await responseC.transform(utf8.decoder).join();
      
      if (responseU.statusCode == 200 && responseC.statusCode == 200) {
        final dataU = jsonDecode(responseBodyU);
        final dataC = jsonDecode(responseBodyC);
        print('✅ إحصائيات المواقع:');
        print('   مواقع U: ${dataU.length > 0 ? dataU[0]['count'] : 'غير محدد'}');
        print('   مواقع C: ${dataC.length > 0 ? dataC[0]['count'] : 'غير محدد'}');
        
        final totalU = dataU.length > 0 ? dataU[0]['count'] : 0;
        final totalC = dataC.length > 0 ? dataC[0]['count'] : 0;
        final expectedU = 25; // U101-U125
        final expectedC = 45; // C101-C145
        
        if (totalU == expectedU && totalC == expectedC) {
          print('   🎉 جميع المواقع الـ 70 موجودة بشكل صحيح!');
        } else {
          print('   ⚠️ عدد المواقع غير مطابق للمتوقع (U:$expectedU, C:$expectedC)');
        }
      }
    } catch (e) {
      print('❌ خطأ في عد المواقع: $e');
    }

    // اختبار 5: فحص الأداء
    print('\n⚡ اختبار 5: فحص الأداء...');
    try {
      final stopwatch = Stopwatch()..start();
      
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/photos?select=id,file_name,location_type,capture_timestamp&limit=50&order=capture_timestamp.desc'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      stopwatch.stop();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        print('✅ اختبار الأداء: ${response.statusCode}');
        print('   عدد النتائج: ${data.length}');
        print('   وقت الاستجابة: ${stopwatch.elapsedMilliseconds}ms');
        
        if (stopwatch.elapsedMilliseconds < 300) {
          print('   🚀 أداء ممتاز جداً!');
        } else if (stopwatch.elapsedMilliseconds < 500) {
          print('   ✅ أداء ممتاز');
        } else if (stopwatch.elapsedMilliseconds < 1000) {
          print('   ✅ أداء جيد');
        } else {
          print('   ⚠️ أداء بطيء');
        }
      }
    } catch (e) {
      print('❌ خطأ في اختبار الأداء: $e');
    }

    client.close();

  } catch (e) {
    print('💥 خطأ عام: $e');
  }

  print('\n' + '=' * 50);
  print('📋 ملخص النتائج:');
  print('✅ = التحسينات مطبقة بنجاح');
  print('❌ = هناك مشكلة تحتاج حل');
  print('⚠️ = تحتاج مراجعة');
}
