// اختبار التحسينات المطبقة على قاعدة البيانات
import 'dart:io';
import 'dart:convert';

void main() async {
  // إعدادات الاتصال
  const supabaseUrl = 'https://xufiuvdtfusbaerwrkzb.supabase.co';
  const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1Zml1dmR0ZnVzYmFlcndya3piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNDA4MjUsImV4cCI6MjA1MDkxNjgyNX0.utjAcWoqFp0DD4IYt9Z-mVOmaLSxoh4yj_frLvOzfrE';

  print('🧪 اختبار التحسينات المطبقة على قاعدة البيانات');
  print('=' * 50);

  try {
    final client = HttpClient();
    
    // اختبار 1: فحص جدول المواقع
    print('\n📍 اختبار 1: فحص جدول المواقع...');
    try {
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/locations?select=count'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        print('✅ جدول المواقع: ${response.statusCode}');
        print('   عدد المواقع: ${data.length > 0 ? data[0]['count'] : 'غير محدد'}');
      } else {
        print('⚠️ جدول المواقع: ${response.statusCode}');
        print('   الرد: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في جدول المواقع: $e');
    }

    // اختبار 2: فحص الدوال المحسنة
    print('\n⚡ اختبار 2: فحص الدوال المحسنة...');
    try {
      final request = await client.postUrl(Uri.parse('$supabaseUrl/rest/v1/rpc/get_photos_sorted_optimized'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final body = jsonEncode({
        'p_sort_by': 'date_desc',
        'p_limit': 5
      });
      request.write(body);
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        print('✅ دالة الصور المحسنة: ${response.statusCode}');
        print('   عدد النتائج: ${data.length}');
      } else {
        print('⚠️ دالة الصور المحسنة: ${response.statusCode}');
        print('   الرد: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في دالة الصور المحسنة: $e');
    }

    // اختبار 3: فحص دالة تحديث الإحصائيات
    print('\n📊 اختبار 3: فحص دالة تحديث الإحصائيات...');
    try {
      final request = await client.postUrl(Uri.parse('$supabaseUrl/rest/v1/rpc/update_all_locations_statistics'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      request.write('{}');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        print('✅ دالة تحديث الإحصائيات: ${response.statusCode}');
        print('   النتيجة: $responseBody');
      } else {
        print('⚠️ دالة تحديث الإحصائيات: ${response.statusCode}');
        print('   الرد: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في دالة تحديث الإحصائيات: $e');
    }

    // اختبار 4: فحص أداء الاستعلامات
    print('\n⏱️ اختبار 4: فحص أداء الاستعلامات...');
    try {
      final stopwatch = Stopwatch()..start();
      
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/photos?select=id,file_name,location_type,capture_timestamp&limit=50&order=capture_timestamp.desc'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      stopwatch.stop();
      
      if (response.statusCode == 200) {
        final data = jsonDecode(responseBody);
        print('✅ استعلام الصور: ${response.statusCode}');
        print('   عدد النتائج: ${data.length}');
        print('   وقت الاستجابة: ${stopwatch.elapsedMilliseconds}ms');
        
        if (stopwatch.elapsedMilliseconds < 500) {
          print('   🚀 أداء ممتاز (أقل من 500ms)');
        } else if (stopwatch.elapsedMilliseconds < 1000) {
          print('   ✅ أداء جيد (أقل من 1000ms)');
        } else {
          print('   ⚠️ أداء بطيء (أكثر من 1000ms)');
        }
      } else {
        print('⚠️ استعلام الصور: ${response.statusCode}');
        print('   الرد: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في اختبار الأداء: $e');
    }

    // اختبار 5: فحص الفهارس
    print('\n📊 اختبار 5: فحص الفهارس المحسنة...');
    try {
      final request = await client.postUrl(Uri.parse('$supabaseUrl/rest/v1/rpc/sql'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final body = jsonEncode({
        'query': "SELECT COUNT(*) as index_count FROM pg_indexes WHERE schemaname = 'public' AND indexname LIKE 'idx_%'"
      });
      request.write(body);
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200) {
        print('✅ فحص الفهارس: ${response.statusCode}');
        print('   النتيجة: $responseBody');
      } else {
        print('⚠️ فحص الفهارس: ${response.statusCode}');
        print('   الرد: $responseBody');
      }
    } catch (e) {
      print('❌ خطأ في فحص الفهارس: $e');
    }

    client.close();

  } catch (e) {
    print('💥 خطأ عام في الاختبار: $e');
  }

  print('\n' + '=' * 50);
  print('📋 ملخص الاختبار:');
  print('- إذا رأيت رموز ✅: التحسينات تعمل بشكل صحيح');
  print('- إذا رأيت رموز ⚠️: هناك مشاكل في الصلاحيات (طبيعي مع RLS)');
  print('- إذا رأيت رموز ❌: هناك مشاكل تقنية تحتاج حل');
  print('- وقت الاستجابة أقل من 500ms يعني أداء ممتاز');
}
