// اختبار بسيط للاتصال بـ Supabase بدون Flutter
import 'dart:io';
import 'dart:convert';

void main() async {
  // إعدادات الاتصال
  const supabaseUrl = 'https://xufiuvdtfusbaerwrkzb.supabase.co';
  const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inh1Zml1dmR0ZnVzYmFlcndya3piIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNDA4MjUsImV4cCI6MjA1MDkxNjgyNX0.utjAcWoqFp0DD4IYt9Z-mVOmaLSxoh4yj_frLvOzfrE';

  print('🔍 Testing Supabase connection...');
  print('URL: $supabaseUrl');
  print('Key: ${supabaseKey.substring(0, 50)}...');

  try {
    // إنشاء HTTP client
    final client = HttpClient();
    
    // اختبار 1: فحص الجداول العامة
    print('\n📊 Testing database access...');
    try {
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/photos?select=count&limit=1'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      print('✅ Photos table response: ${response.statusCode}');
      if (response.statusCode == 200) {
        print('   Data: $responseBody');
      } else {
        print('   Error: $responseBody');
      }
    } catch (e) {
      print('❌ Photos table error: $e');
    }

    // اختبار 2: فحص جدول المستخدمين
    print('\n👥 Testing users table...');
    try {
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/users?select=count&limit=1'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      print('✅ Users table response: ${response.statusCode}');
      if (response.statusCode == 200) {
        print('   Data: $responseBody');
      } else {
        print('   Error: $responseBody');
      }
    } catch (e) {
      print('❌ Users table error: $e');
    }

    // اختبار 3: فحص جدول المواقع
    print('\n📍 Testing locations table...');
    try {
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/locations?select=count&limit=1'));
      request.headers.set('apikey', supabaseKey);
      request.headers.set('Authorization', 'Bearer $supabaseKey');
      request.headers.set('Content-Type', 'application/json');
      
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      print('✅ Locations table response: ${response.statusCode}');
      if (response.statusCode == 200) {
        print('   Data: $responseBody');
      } else {
        print('   Error: $responseBody');
      }
    } catch (e) {
      print('❌ Locations table error: $e');
    }

    // اختبار 4: فحص حالة الخدمة
    print('\n🏥 Testing service health...');
    try {
      final request = await client.getUrl(Uri.parse('$supabaseUrl/rest/v1/'));
      request.headers.set('apikey', supabaseKey);
      
      final response = await request.close();
      print('✅ Service health: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        print('   🟢 Supabase service is healthy');
      } else {
        print('   🟡 Supabase service returned: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Service health error: $e');
    }

    client.close();

  } catch (e) {
    print('💥 Connection failed: $e');
    
    if (e.toString().contains('401')) {
      print('🔑 API Key issue - check your anon key');
    } else if (e.toString().contains('404')) {
      print('🌐 Project not found - check your URL');
    } else if (e.toString().contains('timeout')) {
      print('⏰ Connection timeout - check internet');
    } else if (e.toString().contains('SocketException')) {
      print('🌐 Network connection issue');
    }
  }

  print('\n📋 Test Summary:');
  print('- If you see 200 status codes: ✅ Supabase is working');
  print('- If you see 401 errors: 🔑 API key issue');
  print('- If you see 404 errors: 🗄️ Table/endpoint not found');
  print('- If you see network errors: 🌐 Connection issue');
}
